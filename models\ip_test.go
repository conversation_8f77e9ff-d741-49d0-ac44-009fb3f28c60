package models

import (
	"log"
	"testing"

	"open_geoip/g"

	"github.com/toolkits/file"

	"github.com/stretchr/testify/assert"
)

func init() {
	g.ParseConfig("cfg.json.test")
	if file.IsExist("qqzeng-ip-3.0-ultimate.dat") {
		g.Config().DB.Qqzengip = "qqzeng-ip-3.0-ultimate.dat"
	}
	if file.IsExist("city.free.ipdb") {
		g.<PERSON>fig().DB.Ipdb = "city.free.ipdb"
	}
	err := InitReader()
	if err != nil {
		log.Fatalf("load geo db failed, %v", err)
	}
}

func Test_IpCheck(t *testing.T) {

	var campusIPs = []string{
		"**********/8",
		"***********/24",
		"**********/12",
		"************-**************",
		"*************",
		"2001:da8:8005::/48",
	}

	var ipCheckList = []struct {
		ip  string
		exp bool
	}{
		{"*********", true},
		{"************", false},
		{"*************", true},
		{"************00", true},
		{"***********", true},
		{"*******", false},
		{"*******", false},
		{"2001:da8:8005:abcd:1234::8888", true},
		{"2001:da8:8000:abcd:1234::8888", false},
	}

	for _, r := range ipCheckList {
		out := IPCheck(r.ip, campusIPs)
		assert.Equal(t, out, r.exp)
	}
}

func Test_GetIP(t *testing.T) {

	res, _ := GetIP("***********", g.Config().Source, false, false)
	assert.Equal(t, res.Country, "中国")
	res, _ = GetIP("fd12:3456:789a:bcde::1", g.Config().Source, false, false)
	assert.Equal(t, res.Country, "中国")

	if file.IsExist("city.free.ipdb") {
		g.Config().Source.IPv4 = "ipdb"
		res, _ = GetIP("***************", g.Config().Source, false, false)
		t.Log(res)
		assert.Equal(t, res.Country, "114DNS.COM")
	}
	if file.IsExist("qqzeng-ip-3.0-ultimate.dat") {
		g.Config().Source.IPv4 = "qqzengip"
		res, _ = GetIP("*************", g.Config().Source, false, false)
		t.Log(res)
		assert.Equal(t, res.ISP, "教育网")
	}
	//非法的请求
	_, err := GetIP("201..1", g.Config().Source, false, false)
	assert.NotNil(t, err)
	_, err = GetIP("2001:da8:::::::::", g.Config().Source, false, false)
	assert.NotNil(t, err)
}

func TestSearchIP(t *testing.T) {
	g.Config().Source.IPv4 = "maxmind"
	res := SearchIP("***********", false, false)
	assert.Equal(t, res.Province, "上海")
	assert.Equal(t, res.IP, "***********")
	res = SearchIP("fd12:3456:789a:bcde::1", false, false)
	assert.Equal(t, res.Country, "中国")
	assert.Equal(t, res.IP, "fd12:3456:789a:bcde::1")

	//非法的请求
	res = SearchIP("202..1", false, false)
	assert.Equal(t, res.IP, "202..1")
	assert.Equal(t, res.Country, "")
	res = SearchIP("202:da8:::::::", false, false)
	assert.Equal(t, res.IP, "202:da8:::::::")
	assert.Equal(t, res.Country, "")
}

func Benchmark_maxmind(b *testing.B) {
	for i := 0; i < b.N; i++ {
		SearchIP("*************", false, false)
	}
}

func Benchmark_Ipdb(b *testing.B) {
	if !file.IsExist("city.free.ipdb") {
		return
	}
	g.Config().Source.IPv4 = "ipdb"
	for i := 0; i < b.N; i++ {
		SearchIP("*************", false, false)
	}
}

func Benchmark_qqzengip(b *testing.B) {
	if !file.IsExist("qqzeng-ip-3.0-ultimate.dat") {
		return
	}
	g.Config().Source.IPv4 = "qqzengip"
	for i := 0; i < b.N; i++ {
		SearchIP("*************", false, false)
	}
}
