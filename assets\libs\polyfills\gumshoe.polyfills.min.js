/* gumshoe v4.0.0 | (c) 2019 <PERSON> | MIT License | http://github.com/cferdinandi/gumshoe */
Element.prototype.closest||(Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest=function(b){var a=this;if(!document.documentElement.contains(this)){return null}do{if(a.matches(b)){return a}a=a.parentElement}while(null!==a);return null}),(function(){if("function"==typeof window.CustomEvent){return !1}function a(d,b){b=b||{bubbles:!1,cancelable:!1,detail:void 0};var c=document.createEvent("CustomEvent");return c.initCustomEvent(d,b.bubbles,b.cancelable,b.detail),c}a.prototype=window.Event.prototype,window.CustomEvent=a})(),(function(b,a){"function"==typeof define&&define.amd?define([],(function(){return a(b)})):"object"==typeof exports?module.exports=a(b):b.Gumshoe=a(b)})("undefined"!=typeof global?global:"undefined"!=typeof window?window:this,(function(m){var f={navClass:"active",contentClass:"active",nested:!1,nestedClass:"active",offset:0,reflow:!1,events:!0},h=function(p,a,c){if(c.settings.events){var i=new CustomEvent(p,{bubbles:!0,cancelable:!0,detail:c});a.dispatchEvent(i)}},j=function(c){var a=0;if(c.offsetParent){for(;c;){a+=c.offsetTop,c=c.offsetParent}}return a>=0?a:0},l=function(a){a.sort((function(i,c){return j(i.content)<j(c.content)?-1:1}))},k=function(p,a){var c=p.getBoundingClientRect(),i=(function(e){return"function"==typeof e.offset?parseFloat(e.offset()):parseFloat(e.offset)})(a);return parseFloat(c.top)<=i&&parseFloat(c.bottom)>i},g=function(i,a){if(a.nested){var c=i.parentNode.closest("li");c&&(c.classList.remove(a.nestedClass),g(c,a))}},d=function(i,a){if(i){var c=i.nav.closest("li");c&&(c.classList.remove(a.navClass),i.content.classList.remove(a.contentClass),g(c,a),h("gumshoeDeactivate",c,{link:i.nav,content:i.content,settings:a}))}},b=function(i,a){if(a.nested){var c=i.parentNode.closest("li");c&&(c.classList.add(a.nestedClass),b(c,a))}};return function(s,n){var q,w,e,a,x,r={};r.setup=function(){q=document.querySelectorAll(s),w=[],Array.prototype.forEach.call(q,(function(o){var i=document.getElementById(decodeURIComponent(o.hash.substr(1)));i&&w.push({nav:o,content:i})})),l(w)},r.detect=function(){var i=(function(u,o){for(var p=u.length-1;p>=0;p--){if(k(u[p].content,o)){return u[p]}}})(w,x);i?e&&i.content===e.content||(d(e,x),(function(v,p){if(v){var u=v.nav.closest("li");u&&(u.classList.add(p.navClass),v.content.classList.add(p.contentClass),b(u,p),h("gumshoeActivate",u,{link:v.nav,content:v.content,settings:p}))}})(i,x),e=i):e&&(d(e,x),e=null)};var t=function(i){a&&m.cancelAnimationFrame(a),a=m.requestAnimationFrame(r.detect)},c=function(i){a&&m.cancelAnimationFrame(a),a=m.requestAnimationFrame((function(){l(),r.detect()}))};return r.destroy=function(){e&&d(e),m.removeEventListener("scroll",t,!1),x.reflow&&m.removeEventListener("resize",c,!1),w=null,q=null,e=null,a=null,x=null},r.init=function(i){x=(function(){var o={};return Array.prototype.forEach.call(arguments,(function(p){for(var u in p){if(!p.hasOwnProperty(u)){return}o[u]=p[u]}})),o})(f,i||{}),r.setup(),r.detect(),m.addEventListener("scroll",t,!1),x.reflow&&m.addEventListener("resize",c,!1)},r.init(n),r}}));