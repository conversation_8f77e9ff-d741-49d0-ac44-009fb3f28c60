stages:
  - pre-build
  - build
  - deploy

variables:
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA  # 使用commit的短SHA作为镜像标签

pre-build:
  stage: pre-build
  image: golang:1.20
  script:
    - export GO111MODULE=on
    - export GOPROXY=https://goproxy.cn
    - go mod tidy
    - go build
    - ./open_geoip -csv internal.csv
  artifacts:
    paths:
      - ./
  tags:
    - unicom-docker

build:
  image: docker:19.03.12
  before_script:
   - echo $CI_REGISTRY_PASSWORD | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin
  stage: build
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA .
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  # rules:
  #   - if: $CI_COMMIT_TAG =~ /^release.*/    
  tags:
    - unicom-docker

deploy-open-geoip:
  stage: deploy
  image: ringcentral/sshpass
  tags:
    - unicom-docker
  script:
    - "sshpass -p $SSH_PASSWORD_PRD ssh -p $PORT -o StrictHostKeyChecking=no -t $USER@$IPADDRESS 'docker stop openGeoIp; docker rm openGeoIp; docker pull git.tongji.edu.cn:5050/nic/nic-tool/open-geoip:latest; docker run -d -p 8082:8082 --name openGeoIp git.tongji.edu.cn:5050/nic/nic-tool/open-geoip:latest'"
