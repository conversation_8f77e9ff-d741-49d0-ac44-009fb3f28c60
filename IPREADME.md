# ✅ 需求说明
用户访问网页后，可以看到不同节点目标显示的ip信息，比如
  - 显示访问国内网站的ip
  - 显示访问国外网站的ip
  - 显示访问谷歌的ip
 
 类型于ip111.cn的 “国内测试 / 国外测试 / 谷歌测试”功能。
## 三种可行方案
| 方案 | 是否需要服务器  | 控制力 | 成本 |优点 |缺点| 适用场景
| --- | --- | --- |--- | --- | --- | ---|
| 1. 使用第三方 IP API | ❌ 无需 | ❌ 低 | ✅ 免费 | 快速开发，0 维护 | 不可控、容易失效| 快速上线调试
| 2. 使用 Cloudflare Workers | ❌ 无需 | 中等 | ✅ 免费| 全球 CDN 节点，性能好，易部署| 节点位置不可控、速率有限| 稳定轻量部署
| 3. 自建多节点 /ip 服务 | ✅ 需要 | ✅ 高 |⚠️ 有成本|高度可控，部署灵活| 需要维护，依赖资源|生产/专业服务

## 每种方案详解
### 🟩 方案一：使用第三方 IP API（简单）
#### 示例API:
|名称|地址|是否跨域
| --- | --- | --- |
|ipinfo.io|https://ipinfo.io/ip|✅ 
|api.my-ip.io|https://api.my-ip.io/ip|✅ 
|淘宝IP|https://ip.taobao.com/outGetIpInfo?ip=myip&accessKey=alibaba|❌
### 示例代码：
``` js
fetch('https://ipinfo.io/ip').then(r => r.text()).then(ip => {
  document.getElementById('foreign-ip').innerText = ip;
});
```
### 🟨 方案二：使用 Cloudflare Workers 模拟多个探测节点（推荐）
#### ✅ 原理：
Cloudflare 在全球部署了 CDN 节点，你可以部署多个 Workers，让用户访问最近的节点。
### 示例 Worker 代码：
``` js
export default {
  async fetch(request) {
    return new Response(request.headers.get("cf-connecting-ip"), {
      headers: { "Access-Control-Allow-Origin": "*" }
    });
  }
}
```
部署后创建：
 - cn-node.my-worker.workers.dev
 - us-node.my-worker.workers.dev
 - g-node.my-worker.workers.dev

✅ 优势：
 - 不用你租服务器
 - 免费，每天10万请求
 - 简单稳定

✅ 限制：
 - 节点是 Cloudflare 自动调度的，不能强制固定在哪个国家
 - 不能保证中国 vs 美国 vs Google 网络的完全分流效果

### 🟥 方案三：自建多地区节点（最强控制力）
### ✅ 架构：
你搭建多个 /ip 接口，分别部署在：
|地区|示例平台|示例域名
| --- | --- | --- |
|国内节点|阿里云轻量 / 腾讯云|cn-node.yourdomain.com
|国外节点|Oracle / Vultr / DigitalOcean|us-node.yourdomain.com
|谷歌链路节点|香港、GCP|hk-node.yourdomain.com

### ✅ 接口代码（Node.js）：
``` js
app.get('/ip', (req, res) => {
  const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
  res.send(ip);
});
```
### ✅ 前端统一访问：
``` js
fetchIp('https://cn-node.yourdomain.com/ip', 'cn-ip');
fetchIp('https://us-node.yourdomain.com/ip', 'us-ip');
fetchIp('https://hk-node.yourdomain.com/ip', 'hk-ip');
```
✅ 优点：
- 节点控制最强
- 可接入运营商检测、延迟测速、DNS反解析等

✅ 缺点：
- 需要部署、运维服务器
- 有一定成本（尤其是国内节点）