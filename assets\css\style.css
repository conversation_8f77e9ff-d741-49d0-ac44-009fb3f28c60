@import url("font.css?family=Karla:wght@400;500&family=Lato:wght@400;700&display=swap");

body {
  font-family: "<PERSON><PERSON>", sans-serif;
  overflow-x: hidden !important;
  position: relative;
  color: #404b67;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Lato", sans-serif;
  font-weight: 700;
  line-height: 1.3;
}

.btn,
.btn:focus,
button,
button:focus {
  outline: none !important;
}

a {
  text-decoration: none !important;
  outline: 0;
}

p {
  font-size: 17px;
  line-height: 26px;
}

.row > * {
  position: relative;
}

.section {
  padding-top: 40px;
  padding-bottom: 40px;
  position: relative;
}

.bg-lightan {
  background-color: #fefefe;
}

hr {
  border-top: 1px solid #808eb1;
  margin: 0;
}

.navbar-brand {
  color: #404b67;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  text-decoration: none;
  white-space: nowrap;
}

.line-height_1_4 {
  line-height: 1.4;
}

.line-height_1_6 {
  line-height: 1.6;
}

.line-height_1_8 {
  line-height: 1.8;
}

.fw-normal {
  font-weight: 500 !important;
}

.fw-bold {
  font-weight: 600 !important;
}

.btn {
  padding: 10px 20px;
  font-size: 15px;
  letter-spacing: 0.5px;
  transition: all 0.5s;
}

.btn:focus {
  box-shadow: none;
}

.btn:hover {
  transform: translateY(-5px);
}

.btn:hover:before {
  opacity: 1;
}

.btn-sm {
  padding: 8px 12px;
  font-size: 12px;
}

.shadow {
  box-shadow: 0 30px 30px 0 rgba(64, 75, 103, 0.06) !important;
}

.text-primary {
  color: #1e57c7 !important;
}

.border-primary {
  color: #1e57c7 !important;
}

.btn-primary {
  background: #1e57c7;
  border-color: #1e57c7 !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary.focus,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary .open > .dropdown-toggle.btn-primary {
  background: #1b4fb5;
  border-color: #1b4fb5;
  box-shadow: 0 8px 20px -6px rgba(30, 87, 199, 0.6);
}

.home-overlay {
  height: 100%;
  width: 100%;
  background: linear-gradient(-45deg, #f3f3f3, #efefef, #f2f2f2, #efefef) 0 0;
  background-size: 600% 600%;
  position: absolute;
  animation: hoBg 6s ease infinite;
  right: 0;
  bottom: 0;
  left: 0;
  top: 0;
}

.home-title {
  max-width: 700px;
  font-size: 36px;
  margin: 0 auto;
  margin-top: 0 !important;
}

.home-desc {
  max-width: 600px;
  margin: auto;
  margin-top: 15px !important;
}

.form-select {
  color: #676f86 !important;
}

.home-form {
  background: #fff;
  padding: 20px 21px;
  box-shadow: 0 8px 20px 10px rgba(64, 75, 103, 0.06);
  margin-top: 30px !important;
}

.home-form .form-control {
  border: 1px solid #dfe9f1;
  height: 54px !important;
  font-size: 15px;
  border-radius: 0.25rem;
}

.home-form .form-control ::placeholder {
  color: #676f86;
}

.home-form .form-control:focus {
  border-color: #e4eef7;
}

.home-form .home-button .btn {
  padding-top: 15px;
  padding-bottom: 15px;
}

textarea.form-control {
  height: auto !important;
}

.form-control {
  box-shadow: none !important;
  height: 42px;
  padding-left: 20px;
  border: 1px solid #dfe9f1;
  font-size: 16px;
  border-radius: 5px;
}

.form-control:focus {
  border-color: #e4eef7;
}

.bg-footer {
  padding: 20px 0;
}

.footer-link a {
  color: #808eb1;
  line-height: 38px;
  font-size: 17px;
  -webkit-transition: all 0.5s;
  background-color: transparent;
  transition: all 0.5s;
}

.footer-link a:hover {
  color: #1e57c7;
}

.footer-social i {
  width: 36px;
  height: 36px;
  display: inline-block;
  line-height: 36px;
  border-radius: 50%;
  text-align: center;
  background: #1e57c7;
  font-size: 15px;
  color: #fff;
}

@media (min-width: 200px) and (max-width: 1024px) {
  .home-title {
    font-size: 32px;
  }

  .logo .logo-light {
    display: none;
  }

  .logo .logo-dark {
    display: inline-block;
  }
}

@media (max-width: 768px) {
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px !important;
  }
}

@keyframes hoBg {
  0% {
    background-position: 0 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
