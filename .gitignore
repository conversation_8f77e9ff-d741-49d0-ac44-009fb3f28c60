# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.sha256
*.dat
*.tar.gz
*.zip
go-geoip

# vscode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# idea
.idea
.DS_Store

# Local History for Visual Studio Code
.history/

# config
# *.json

# logs
logs/*

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# 程序构建生成文件
/var
gitversion
open-geoip-0.4.0.tar.gz
open_geoip