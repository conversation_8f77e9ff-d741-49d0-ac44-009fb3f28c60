package controller

import (
	"net"
	"net/http"
	"strings"

	"open_geoip/g"

	"github.com/gin-contrib/sessions"

	"open_geoip/models"

	"github.com/gin-gonic/gin"
)

func geoIpApi(c *gin.Context) {
	isAuth := false
	ipAddr := c.Query("ip")
	// 去掉左右空格
	ipAddr = strings.TrimSpace(ipAddr)
	if !models.CheckIPValid(ipAddr) {
		c.String(http.StatusOK, "不是合法的IP地址")
		return
	}
	realIP := c.Request.Header.Get("X-Real-IP")
  if realIP == "" {
      realIP = c.Request.Header.Get("X-Forwarded-For")
  }
  if realIP == "" {
      realIP = c.ClientIP()
  }
	if err := models.SetQueryRateLimit(g.Config().RateLimit.Enabled, realIP); err != nil {
		c.String(http.StatusOK, err.Error())
		return
	}
	if g.Config().SSO.Enabled {
		session := sessions.Default(c)
		u := session.Get("username")
		if u != nil {
			isAuth = true
		}
	}

	c.String(http.StatusOK, models.SearchIP(ipAddr, false, isAuth).ToString())
}
func renderCloudflareProbe(c *gin.Context) {
	c.HTML(http.StatusOK, "cloudflare-probe.html", gin.H{
		"title": "出口 IP 探测页面",
	})
}

func getMyIP(c *gin.Context) {
	realIP := c.Request.Header.Get("X-Real-IP")
	if realIP == "" {
		realIP = c.Request.Header.Get("X-Forwarded-For")
	}
	if realIP == "" {
		realIP = c.ClientIP()
	}
	c.String(http.StatusOK, realIP)
}

/**
 * 判断IP是否在校园网内
 * 校园网IP范围：
 */
func isIPInCampusNetwork(c *gin.Context) {
	realIP := c.Request.Header.Get("X-Real-IP")
  if realIP == "" {
      realIP = c.Request.Header.Get("X-Forwarded-For")
  }
  if realIP == "" {
      realIP = c.ClientIP()
  }
	ip := net.ParseIP(realIP)
	_, ipNet, _ := net.ParseCIDR("***********/16")
	_, ipNet1, _ := net.ParseCIDR("***********/17")
	_, ipNet2, _ := net.ParseCIDR("10.0.0.0/8")
	_, ipNet3, _ := net.ParseCIDR("**********/10")
	_, ipNet4, _ := net.ParseCIDR("*************/19")
	if ipNet.Contains(ip) {
		c.String(http.StatusOK, "true")
		return
	}
	if ipNet1.Contains(ip) {
		c.String(http.StatusOK, "true")
		return
	}
	if ipNet2.Contains(ip) {
		c.String(http.StatusOK, "true")
		return
	}
	if ipNet3.Contains(ip) {
		c.String(http.StatusOK, "true")
		return
	}
	if ipNet4.Contains(ip) {
		c.String(http.StatusOK, "true")
		return
	}
	c.String(http.StatusOK, "false")
}

func getMyIPFormat(c *gin.Context) {
	realIP := c.Request.Header.Get("X-Real-IP")
  if realIP == "" {
      realIP = c.Request.Header.Get("X-Forwarded-For")
  }
  if realIP == "" {
      realIP = c.ClientIP()
  }
	res := map[string]string{
		"ip": realIP,
	}
	c.JSON(http.StatusOK, SuccessRes(res))
}

func getMyLocation(c *gin.Context) {
	realIP := c.Request.Header.Get("X-Real-IP")
  if realIP == "" {
      realIP = c.Request.Header.Get("X-Forwarded-For")
  }
  if realIP == "" {
      realIP = c.ClientIP()
  }
	c.String(http.StatusOK, models.SearchIP(realIP, true, false).ToString())
}

func getMyLocationFormat(c *gin.Context) {
	realIP := c.Request.Header.Get("X-Real-IP")
  if realIP == "" {
      realIP = c.Request.Header.Get("X-Forwarded-For")
  }
  if realIP == "" {
      realIP = c.ClientIP()
  }
	c.JSON(http.StatusOK, SuccessRes(models.SearchIP(realIP, true, false)))
}

func openGetIpApi(c *gin.Context) {
	ipAddr := c.Query("ip")
	if !models.CheckIPValid(ipAddr) {
		c.JSON(http.StatusOK, ErrorRes(ParamValueError, "不是合法的IP地址"))
		return
	}
	res := models.SearchIP(ipAddr, true, false)
	c.JSON(http.StatusOK, SuccessRes(res))
}
