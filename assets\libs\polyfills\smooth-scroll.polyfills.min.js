/* SmoothScroll v16.1.4 | (c) 2020 <PERSON> | MIT License | http://github.com/cferdinandi/smooth-scroll */
!function(a,b){"object"==typeof exports&&"undefined"!=typeof module?module.exports=b():"function"==typeof define&&define.amd?define(b):(a=a||self).SmoothScroll=b()}(this,(function(){window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(a){var l,i=(this.document||this.ownerDocument).querySelectorAll(a),k=this;do{for(l=i.length;--l>=0&&i.item(l)!==k;){}}while(l<0&&(k=k.parentElement));return k}),function(){if("function"==typeof window.CustomEvent){return !1}function a(i,l){l=l||{bubbles:!1,cancelable:!1,detail:void 0};var k=document.createEvent("CustomEvent");return k.initCustomEvent(i,l.bubbles,l.cancelable,l.detail),k}a.prototype=window.Event.prototype,window.CustomEvent=a}(),function(){for(var a=0,k=["ms","moz","webkit","o"],i=0;i<k.length&&!window.requestAnimationFrame;++i){window.requestAnimationFrame=window[k[i]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[k[i]+"CancelAnimationFrame"]||window[k[i]+"CancelRequestAnimationFrame"]}window.requestAnimationFrame||(window.requestAnimationFrame=function(q,m){var p=(new Date).getTime(),l=Math.max(0,16-(p-a)),e=window.setTimeout((function(){q(p+l)}),l);return a=p+l,e}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(l){clearTimeout(l)})}();var c={ignore:"[data-scroll-ignore]",header:null,topOnEmptyHash:!0,speed:500,speedAsDuration:!1,durationMax:null,durationMin:null,clip:!0,offset:0,easing:"easeInOutCubic",customEasing:null,updateURL:!0,popstate:!0,emitEvents:!0},j=function(){var a={};return Array.prototype.forEach.call(arguments,(function(i){for(var e in i){if(!i.hasOwnProperty(e)){return}a[e]=i[e]}})),a},f=function(l){"#"===l.charAt(0)&&(l=l.substr(1));for(var u,p=String(l),q=p.length,m=-1,k="",s=p.charCodeAt(0);++m<q;){if(0===(u=p.charCodeAt(m))){throw new InvalidCharacterError("Invalid character: the input contains U+0000.")}u>=1&&u<=31||127==u||0===m&&u>=48&&u<=57||1===m&&u>=48&&u<=57&&45===s?k+="\\"+u.toString(16)+" ":k+=u>=128||45===u||95===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122?p.charAt(m):"\\"+p.charAt(m)}return"#"+k},g=function(){return Math.max(document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight,document.body.clientHeight,document.documentElement.clientHeight)},d=function(a){return a?(i=a,parseInt(window.getComputedStyle(i).height,10)+a.offsetTop):0;var i},b=function(a,k,i){0===a&&document.body.focus(),i||(a.focus(),document.activeElement!==a&&(a.setAttribute("tabindex","-1"),a.focus(),a.style.outline="none"),window.scrollTo(0,k))},h=function(a,p,l,m){if(p.emitEvents&&"function"==typeof window.CustomEvent){var k=new CustomEvent(a,{bubbles:!0,detail:{anchor:l,toggle:m}});document.dispatchEvent(k)}};return function(a,q){var p,n,e,o,i={};i.cancelScroll=function(l){cancelAnimationFrame(o),o=null,l||h("scrollCancel",p)},i.animateScroll=function(H,t,L){i.cancelScroll();var F=j(p||c,L||{}),P="[object Number]"===Object.prototype.toString.call(H),D=P||!H.tagName?null:H;if(P||D){var J=window.pageYOffset;F.header&&!e&&(e=document.querySelector(F.header));var B,Q,N,K=d(e),z=P?H:function(u,y,w,v){var l=0;if(u.offsetParent){do{l+=u.offsetTop,u=u.offsetParent}while(u)}return l=Math.max(l-y-w,0),v&&(l=Math.min(l,g()-window.innerHeight)),l}(D,K,parseInt("function"==typeof F.offset?F.offset(H,t):F.offset,10),F.clip),s=z-J,m=g(),I=0,x=function(l,v){var u=v.speedAsDuration?v.speed:Math.abs(l/1000*v.speed);return v.durationMax&&u>v.durationMax?v.durationMax:v.durationMin&&u<v.durationMin?v.durationMin:parseInt(u,10)}(s,F),G=function(l){B||(B=l),I+=l-B,N=J+s*function(u,w){var v;return"easeInQuad"===u.easing&&(v=w*w),"easeOutQuad"===u.easing&&(v=w*(2-w)),"easeInOutQuad"===u.easing&&(v=w<0.5?2*w*w:(4-2*w)*w-1),"easeInCubic"===u.easing&&(v=w*w*w),"easeOutCubic"===u.easing&&(v=--w*w*w+1),"easeInOutCubic"===u.easing&&(v=w<0.5?4*w*w*w:(w-1)*(2*w-2)*(2*w-2)+1),"easeInQuart"===u.easing&&(v=w*w*w*w),"easeOutQuart"===u.easing&&(v=1- --w*w*w*w),"easeInOutQuart"===u.easing&&(v=w<0.5?8*w*w*w*w:1-8*--w*w*w*w),"easeInQuint"===u.easing&&(v=w*w*w*w*w),"easeOutQuint"===u.easing&&(v=1+ --w*w*w*w*w),"easeInOutQuint"===u.easing&&(v=w<0.5?16*w*w*w*w*w:1+16*--w*w*w*w*w),u.customEasing&&(v=u.customEasing(w)),v||w}(F,Q=(Q=0===x?0:I/x)>1?1:Q),window.scrollTo(0,Math.floor(N)),function(u,w){var v=window.pageYOffset;if(u==w||v==w||(J<w&&window.innerHeight+v)>=m){return i.cancelScroll(!0),b(H,w,P),h("scrollStop",F,H,t),B=null,o=null,!0}}(N,z)||(o=window.requestAnimationFrame(G),B=l)};0===window.pageYOffset&&window.scrollTo(0,0),function(l,v,u){v||history.pushState&&u.updateURL&&history.pushState({smoothScroll:JSON.stringify(u),anchor:l.id},document.title,l===document.documentElement?"#top":"#"+l.id)}(H,P,F),"matchMedia" in window&&window.matchMedia("(prefers-reduced-motion)").matches?b(H,Math.floor(z),!1):(h("scrollStart",F,H,t),i.cancelScroll(!0),window.requestAnimationFrame(G))}};var r=function(l){if(!l.defaultPrevented&&!(0!==l.button||l.metaKey||l.ctrlKey||l.shiftKey)&&"closest" in l.target&&(n=l.target.closest(a))&&"a"===n.tagName.toLowerCase()&&!l.target.closest(p.ignore)&&n.hostname===window.location.hostname&&n.pathname===window.location.pathname&&/#/.test(n.href)){var s,m;try{s=f(decodeURIComponent(n.hash))}catch(l){s=f(n.hash)}if("#"===s){if(!p.topOnEmptyHash){return}m=document.documentElement}else{m=document.querySelector(s)}(m=m||"#top"!==s?m:document.documentElement)&&(l.preventDefault(),function(u){if(history.replaceState&&u.updateURL&&!history.state){var v=window.location.hash;v=v||"",history.replaceState({smoothScroll:JSON.stringify(u),anchor:v||window.pageYOffset},document.title,v||window.location.href)}}(p),i.animateScroll(m,n))}},k=function(){if(null!==history.state&&history.state.smoothScroll&&history.state.smoothScroll===JSON.stringify(p)){var l=history.state.anchor;"string"==typeof l&&l&&!(l=document.querySelector(f(history.state.anchor)))||i.animateScroll(l,null,{updateURL:!1})}};i.destroy=function(){p&&(document.removeEventListener("click",r,!1),window.removeEventListener("popstate",k,!1),i.cancelScroll(),p=null,n=null,e=null,o=null)};return function(){if(!("querySelector" in document&&"addEventListener" in window&&"requestAnimationFrame" in window&&"closest" in window.Element.prototype)){throw"Smooth Scroll: This browser does not support the required JavaScript methods and browser APIs."}i.destroy(),p=j(c,q||{}),e=p.header?document.querySelector(p.header):null,document.addEventListener("click",r,!1),p.updateURL&&p.popstate&&window.addEventListener("popstate",k,!1)}(),i}}));