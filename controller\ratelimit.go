package controller

import (
	"net/http"

	"open_geoip/models"

	"github.com/gin-gonic/gin"
)

func getRateLimit(c *gin.Context) {
	clientIP := c.Query("clientip")
	currentRateLimit, err := models.GetCurrentRateCount(clientIP)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Error<PERSON><PERSON>(InternalAPIError, err.Error()))
		return
	}
	c.JSO<PERSON>(http.StatusOK, SuccessRes(currentRateLimit))
}

func clearRateLimit(c *gin.Context) {
	clientIP := c.Query("clientip")
	if err := models.ClearRateLimit(clientIP); err != nil {
		c.<PERSON>SO<PERSON>(http.StatusInternalServerError, Error<PERSON>es(InternalAPIError, err.Error()))
		return
	}
	c.<PERSON>(http.StatusOK, SuccessRes(nil))
}
